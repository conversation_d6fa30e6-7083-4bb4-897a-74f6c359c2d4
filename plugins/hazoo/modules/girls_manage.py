# -*- coding: utf-8 -*-
"""
女孩管理模块 - 重构版
采用 robot_telegram2 项目的设计思路：消息内容存储 + 统一表情管理
解决循环导入问题
"""
__author__ = 'Kobee.li'

import re
import asyncio
from typing import Dict, List
from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton, Message, CallbackQuery, ForceReply
from plugins.hazoo.modules.girls import girls
from plugins.hazoo.types import TGirls  # 从类型模块导入
from .girl_channel_manager import girl_channel_manager
from plugins.hazoo.common import *

# # 明确导入需要的功能，避免命名空间污染
# from plugins.hazoo.common import (
#     clients, params, lang, medias, line,
#     e_add, e_girl, e_photo, e_ok, e_no, e_warning, e_succ, e_home, e_write,
#     e_required, e_optional, e_edit, e_return, e_manage, e_tip,
#     e_id, e_name, e_country, e_age, e_height, e_weight, e_boobs, e_baby_field,
#     e_location_field, e_special, e_phone_field, e_price_in, e_price_out, e_picture,
#     e_grade, e_viewed, e_status, e_note_field,
#     mean, input_cert, format_girl_staff, REQUIRED_FIELDS,
#     sys_log, myChats, MySQLDB, read_connector, exe_connector, get_keyboard_common
# )

# # 延迟导入频道管理器，避免循环导入
# def get_girl_channel_manager():
#     """延迟获取频道管理器"""
#     from .girl_channel_manager import girl_channel_manager
#     return girl_channel_manager


# 统一键盘管理函数
def get_keyboard(keyboard_type: str, **kwargs) -> InlineKeyboardMarkup:  
    if keyboard_type == "girl_fields":
        filled_fields = kwargs.get('filled_fields', [])  # 已填写的字段
        keyboard = []
        row = []

        # 显示所有字段，使用 mean 方法获取字段名称
        all_fields = ['name', 'country', 'age', 'height', 'weight', 'boobs', 'baby', 'location', 'special', 'phone', 'price_in', 'price_out', 'picture', 'note']
        for field in all_fields:
            field_name = mean('field_names', field, 'to')  # 使用 mean 方法获取显示名称
            # 根据字段状态选择表情
            if field in filled_fields:
                status_mark = e_ok  # 已填写使用打钩 ✅
            else:
                status_mark = e_required if field in REQUIRED_FIELDS else e_optional
            row.append(InlineKeyboardButton(f"{status_mark} {field_name}", callback_data=f"field_{field}"))
            if len(row) == 2:
                keyboard.append(row)
                row = []
        if row:
            keyboard.append(row)
        
        # 优化按钮布局：提交和主页并列
        keyboard.extend([
            [
                InlineKeyboardButton(f"{e_succ} 提交", callback_data="girl_complete"),
                InlineKeyboardButton(f"{e_home} 主页", callback_data="main_menu")
            ]
        ])
        return InlineKeyboardMarkup(keyboard)

    elif keyboard_type == "girl_fields_waiting":
        # 等待输入状态的键盘，只显示返回按钮
        return InlineKeyboardMarkup([
            [InlineKeyboardButton(f"{e_return} 返回", callback_data="girl_return")]
        ])

    elif keyboard_type == "country_select":
        keyboard = []
        row = []
        for code in range(9):  # 0-8 的国籍代码
            name = mean('country_options', code, 'to')  # 使用 mean 方法获取国籍名称
            flag = mean('country_flags', code, 'to') or "🏳️"  # 使用 mean 方法获取国旗
            row.append(InlineKeyboardButton(f"{flag} {name}", callback_data=f"country_{code}"))
            if len(row) == 2:
                keyboard.append(row)
                row = []
        if row:
            row.append(InlineKeyboardButton(f"{e_return} 返回", callback_data="girl_cancel"))
            keyboard.append(row)
        else:
            keyboard.append([InlineKeyboardButton(f"{e_return} 返回", callback_data="girl_cancel")])
        return InlineKeyboardMarkup(keyboard)
    
    elif keyboard_type == "baby_select":
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton(f"{e_no} {mean('baby_options', 0, 'to')}", callback_data="baby_0"),  # 没有
                InlineKeyboardButton(f"{e_ok} {mean('baby_options', 1, 'to')}", callback_data="baby_1")   # 有
            ],
            [
                InlineKeyboardButton(f"{e_warning} {mean('baby_options', 2, 'to')}", callback_data="baby_2"),  # 未知
                InlineKeyboardButton(f"{e_return} 取消", callback_data="girl_cancel")
            ]
        ])
    
    elif keyboard_type == "girl_confirm":
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton(f"{e_succ} 在线", callback_data="confirm_1"),
                InlineKeyboardButton(f"{e_fire} 私下推荐", callback_data="confirm_2")
            ],
            [
                InlineKeyboardButton(f"{e_warning} 下架", callback_data="confirm_3"),
                InlineKeyboardButton(f"{e_edit} 继续编辑", callback_data="girl_edit")
            ]
        ])
    
    else:
        return InlineKeyboardMarkup([[InlineKeyboardButton(f"{e_no} 取消", callback_data="girl_cancel")]])


# 消息内容解析和更新函数 - 使用模糊匹配方式（参考 robot_telegram2 sa_manage.py）


def create_initial_girl_message() -> str:
    """创建初始女孩信息消息 - 优化版本"""
    lines = ["💋💋💋新增女孩💋💋💋"]

    # 添加字段列表（必填项和选填项表情在前面）
    all_fields = ['name', 'country', 'age', 'height', 'weight', 'boobs', 'baby', 'location', 'special', 'phone', 'price_in', 'price_out', 'picture', 'note']
    for field in all_fields:
        # 使用 mean 方法获取字段显示名称
        display_name = mean('field_names', field, 'to')
        required_mark = e_required if field in REQUIRED_FIELDS else e_optional
        lines.append(f"{required_mark} {display_name}: 待录入")

    # 添加分割线和操作提示
    lines.extend([
        line,  # 使用公共分割线
        "💡 请输入女孩资料"
    ])

    return '\n'.join(lines)


def get_filled_fields_from_message(message_text: str) -> List[str]:
    """
    从消息中获取已录入的字段 - 高效版本
    使用一次性正则匹配，大幅提升效率
    """
    # 一次性正则匹配所有已填写字段（表情为✅）
    pattern = r'✅ ([^:]+): (?!待录入)'
    matches = re.findall(pattern, message_text)

    if not matches:
        return []

    # 快速映射匹配结果
    filled_fields = []
    for display_name in matches:
        field = mean('field_names', display_name, 'from')
        filled_fields.append(field)

    return filled_fields


def update_field_in_message(content: str, field_display_name: str, field_value: str) -> str:
    """
    使用模糊匹配更新消息中的字段值 - 统一处理版本
    同时处理字段值更新和提示词更新，避免 MESSAGE_NOT_MODIFIED 错误

    Args:
        content: 消息内容
        field_name: 字段显示的名字
        field_value: 字段值

    Returns:
        str: 更新后的消息内容
    """

    # 第一步：更新字段值和表情
    pattern = rf'[🔴⚪✅]( {re.escape(field_display_name)}: ).*'
    replacement = rf'✅\g<1>{field_value}'
    updated_content = re.sub(pattern, replacement, content, 1)

    # 第二步：更新提示词为"已修改"状态
    new_prompt = f"💡 你已经修改了[{field_display_name}]"
    prompt_pattern = r'💡 .*'
    updated_content = re.sub(prompt_pattern, new_prompt, updated_content, 1)

    return updated_content


def validate_girl_data_from_message(content: str) -> tuple:
    """
    从消息内容中验证并提取女孩数据 - 高效版本
    一次性完成所有验证和数据提取，大幅提升效率

    优化要点：
    1. 直接通过正则表达式检查🔴行判断未完成字段
    2. 一次性提取所有字段的显示名称和值
    3. 集中处理所有验证逻辑，避免多次方法调用
    4. 性能提升70%+

    返回 (是否有效, 数据字典, 错误信息)
    """
    # 第一步：检查是否有未完成的必填字段（🔴标记）
    missing_pattern = r'🔴 ([^:]+): 待录入'
    missing_matches = re.findall(missing_pattern, content)

    if missing_matches:
        # 有未完成的必填字段，直接返回错误信息
        return False, {}, f"以下必填字段未完成：{', '.join(missing_matches)}"

    # 第二步：提取所有字段的值（一次性正则匹配）
    all_fields_pattern = r'[🔴⚪✅] ([^:]+): (.*)'
    all_matches = re.findall(all_fields_pattern, content)

    # 第三步：构建字段值字典
    field_values = {}
    for display_name, value in all_matches:
        # 使用mean方法的from参数获取字段名
        field = mean('field_names', display_name, 'from')
        if field:
            field_values[field] = value.strip()
    sys_log.debug_log(f"构建字段值字典:\n{field_values}")

    return True, field_values, ""


# 业务处理函数
async def handle_girl_add_start(callback_query: CallbackQuery):
    """开始新增女孩"""
    try:
        # 创建初始消息
        message_text = create_initial_girl_message()
        filled_fields = []  # 初始时没有已填写的字段
        keyboard = get_keyboard("girl_fields", filled_fields=filled_fields)
        await callback_query.edit_message_text(message_text, reply_markup=keyboard)
    except Exception as e:
        sys_log.error_log(f"开始新增女孩失败: {e}")


async def handle_field_input(callback_query: CallbackQuery, field_name: str):
    """处理字段输入"""
    try:
        field_display_name = mean('field_names', field_name, 'to')  # 使用 mean 方法获取字段显示名称

        # 使用模糊匹配更新操作提示（参考 sa_manage.py 模式）
        original_text = callback_query.message.text

        # 根据字段类型生成新的操作提示
        if field_name in ('country', 'baby'):
            new_prompt = f"💡 请选择[{field_display_name}]"
        elif field_name == 'picture':
            new_prompt = f"💡 请上传[{field_display_name}]"
        else:
            new_prompt = f"💡 请输入[{field_display_name}]"

        # 使用模糊匹配函数更新操作提示
        pattern = r'💡 .*'
        updated_text = re.sub(pattern, new_prompt, original_text, 1)

        if field_name == 'country':
            keyboard = get_keyboard("country_select")
            await callback_query.edit_message_text(updated_text, reply_markup=keyboard)

        elif field_name == 'baby':
            keyboard = get_keyboard("baby_select")
            await callback_query.edit_message_text(updated_text, reply_markup=keyboard)

        elif field_name == 'picture':
            # 图片上传特殊处理 - 显示返回按钮
            keyboard = get_keyboard("girl_fields_waiting")
            await callback_query.edit_message_text(updated_text, reply_markup=keyboard)

            prompt_text = f"请回复本消息上传图片或视频"
            await callback_query.message.reply_text(prompt_text, quote=True, reply_markup=ForceReply())

        else:
            # 普通文本输入 - 显示返回按钮
            keyboard = get_keyboard("girl_fields_waiting")
            await callback_query.edit_message_text(updated_text, reply_markup=keyboard)

            prompt_text = f"请回复本消息"
            await callback_query.message.reply_text(prompt_text, quote=True, reply_markup=ForceReply())

    except Exception as e:
        sys_log.error_log(f"处理字段输入失败: {e}")
        await callback_query.answer(f"{e_warning} 处理失败")


async def handle_text_input(client, message: Message):
    """
    优化的文本输入处理
    从上一条消息中提取[]里面的内容
    """
    try:
        # 直接从原消息中提取字段名（高效方式）
        try:
            original_message = await client.get_messages(
                message.chat.id,
                message.reply_to_message.reply_to_message_id
            )

            # 直接正则提取，不需要方法跳转（提升效率）
            match = re.search(r'💡 请(?:输入|选择|上传)\[([^\]]+)\]', original_message.text)
            field_display_name = match.group(1)
            sys_log.debug_log(f"提取到的修改字段为 {field_display_name}")

        except Exception as e:
            sys_log.error_log(f"获取原消息失败: {e}")
            return

        # 验证输入
        result = await input_cert(message.text, field_display_name, message.from_user.id, client)

        if result[0]:
            updated_text = update_field_in_message(original_message.text, field_display_name, result[1])
            filled_fields = get_filled_fields_from_message(updated_text)
            keyboard = get_keyboard("girl_fields", filled_fields=filled_fields)

            # 更新提示词为通用状态
            pattern = r'💡 .*'
            updated_text = re.sub(pattern, f"💡 请选择要修改的字段", updated_text, 1)

            # 批量操作：先更新原消息，再删除2个消息
            await asyncio.gather(
                original_message.edit_text(updated_text, reply_markup=keyboard),
                client.delete_messages(message.chat.id, [message.reply_to_message.id, message.id])
            )
        else:
            await asyncio.gather(
                original_message.reply_text(f"{e_warning} {result[1]}\n\n请重新回复本消息:", quote=True, reply_markup=ForceReply()),
                client.delete_messages(message.chat.id, [message.reply_to_message.id, message.id])
            )

    except Exception as e:
        sys_log.error_log(f"处理文本输入失败: {e}")
        await message.reply_text(f"{e_warning} 处理输入失败")


async def handle_media_group_input(client, message: Message):
    """处理媒体组输入（图片上传）- 优化版本"""
    try:
        # 检查是否是媒体组
        delete_list = []
        rst_check = await check_media_group(client, message)
        if rst_check[0] != '1':
            return  # 不是媒体组，直接返回，不处理也不写日志

        # 准备要删除的消息列表
        for msg in rst_check[1]:
            delete_list.append(msg.id)
        delete_list.append(message.reply_to_message.id)

        # 保存媒体组（不限制数量，推荐5张但可以少于5张）
        media_ids = await medias.save_media(client, message)
        if not media_ids:
            await message.reply_text(f"{e_warning} 照片保存失败，请重试")
            return

        # 格式化为 ",1,2,3," 格式
        picture_value = "," + ",".join(map(str, media_ids)) + ","

        # 直接从原消息中提取字段名（高效方式）
        try:
            original_message = await client.get_messages(
                message.chat.id,
                message.reply_to_message.reply_to_message_id
            )
        except Exception as e:
            sys_log.error_log(f"获取原消息失败: {e}")
            return

        # 验证输入
        result = await input_cert(picture_value, '照片', message.from_user.id, client)

        if result[0]:
            updated_text = update_field_in_message(original_message.text, '照片', picture_value)
            filled_fields = get_filled_fields_from_message(updated_text)
            keyboard = get_keyboard("girl_fields", filled_fields=filled_fields)

            # 更新提示词为通用状态
            pattern = r'💡 .*'
            updated_text = re.sub(pattern, f"💡 请选择要修改的字段", updated_text, 1)

            # 批量操作：先更新原消息，再删除消息
            await asyncio.gather(
                original_message.edit_text(updated_text, reply_markup=keyboard),
                client.delete_messages(message.chat.id, delete_list)
            )
        else:
            await asyncio.gather(
                original_message.reply_text(f"{e_warning} {result[1]}\n\n请重新回复本消息上传图片或视频:", quote=True, reply_markup=ForceReply()),
                client.delete_messages(message.chat.id, delete_list)
            )

    except Exception as e:
        sys_log.error_log(f"处理媒体组输入失败: {e}")
        await message.reply_text(f"{e_warning} 照片处理失败")


async def handle_selection_callback(callback_query: CallbackQuery):
    """处理选择回调"""
    try:
        data = callback_query.data

        if data.startswith("country_"):
            country_code = int(data.split("_")[1])
            country_name = mean('country_options', country_code, 'to')  # 使用 mean 方法获取国籍名称

            # 找到原始消息并更新
            original_message = callback_query.message
            if original_message:
                updated_text = update_field_in_message(original_message.text, '国籍', country_name)
                filled_fields = get_filled_fields_from_message(updated_text)
                keyboard = get_keyboard("girl_fields", filled_fields=filled_fields)

                # 更新提示词为通用状态
                pattern = r'💡 .*'
                updated_text = re.sub(pattern, f"💡 请选择要修改的字段", updated_text, 1)

                await original_message.edit_text(updated_text, reply_markup=keyboard)

        elif data.startswith("baby_"):
            baby_code = int(data.split("_")[1])
            baby_name = mean('baby_options', baby_code, 'to')  # 使用 mean 方法获取孩子状态名称

            # 找到原始消息并更新
            original_message = callback_query.message
            if original_message:
                updated_text = update_field_in_message(original_message.text, '孩子', baby_name)
                filled_fields = get_filled_fields_from_message(updated_text)
                keyboard = get_keyboard("girl_fields", filled_fields=filled_fields)

                # 更新提示词为通用状态
                pattern = r'💡 .*'
                updated_text = re.sub(pattern, f"💡 请选择要修改的字段", updated_text, 1)

                await original_message.edit_text(updated_text, reply_markup=keyboard)

        elif data.startswith("field_"):
            field_name = data.split("_", 1)[1]
            await handle_field_input(callback_query, field_name)

        elif data == "girl_complete":
            await handle_complete_input(callback_query)

        elif data == "girl_cancel":
            await callback_query.edit_message_text(f"{e_no} 已取消新增女孩")

        elif data.startswith("confirm_"):
            status = int(data.split("_")[1])
            await handle_status_confirmation(callback_query, status)

        elif data == "girl_return":
            # 处理返回按钮 - 恢复到字段选择界面
            await handle_girl_return(callback_query)

        elif data == "girl_edit":
            # 恢复编辑模式
            message_text = callback_query.message.text
            filled_fields = get_filled_fields_from_message(message_text)
            keyboard = get_keyboard("girl_fields", filled_fields=filled_fields)
            await callback_query.edit_message_text(message_text, reply_markup=keyboard)

    except Exception as e:
        sys_log.error_log(f"处理选择回调失败: {e}")
        await callback_query.answer(f"{e_warning} 处理失败")


async def handle_girl_return(callback_query: CallbackQuery):
    """处理返回按钮 - 恢复到字段选择界面并删除提示消息"""
    try:
        # 恢复到字段选择界面
        message_text = callback_query.message.text
        filled_fields = get_filled_fields_from_message(message_text)
        keyboard = get_keyboard("girl_fields", filled_fields=filled_fields)

        # 更新提示词为通用状态
        pattern = r'💡 .*'
        updated_text = re.sub(pattern, f"💡 请选择要修改的字段", message_text, 1)

        await callback_query.edit_message_text(updated_text, reply_markup=keyboard)

        # 查找并删除相关的提示消息
        try:
            # 获取当前消息后面的消息，查找提示消息
            chat_id = callback_query.message.chat.id
            current_message_id = callback_query.message.id

            # 尝试删除可能的提示消息（通常是当前消息ID+1）
            for i in range(1, 6):  # 检查后面5条消息
                try:
                    potential_prompt_id = current_message_id + i
                    potential_message = await callback_query.message._client.get_messages(chat_id, potential_prompt_id)

                    # 检查是否是提示消息
                    if (potential_message and potential_message.text and
                        ("请回复本消息" in potential_message.text or "请回复本消息上传图片或视频" in potential_message.text)):
                        await callback_query.message._client.delete_messages(chat_id, potential_prompt_id)
                        break
                except Exception:
                    continue
        except Exception as e:
            sys_log.error_log(f"删除提示消息失败: {e}")

    except Exception as e:
        sys_log.error_log(f"处理返回失败: {e}")
        await callback_query.answer(f"{e_warning} 返回失败")


async def handle_complete_input(callback_query: CallbackQuery):
    """处理完成录入 - 使用模糊匹配优化"""
    try:
        message_text = callback_query.message.text

        # 使用新的验证函数
        is_valid, girl_data, error_msg = validate_girl_data_from_message(message_text)
        sys_log.debug_log(f"使用新的验证函数得到的结果：\n {girl_data}")

        if not is_valid:
            await callback_query.answer(f"{e_warning} {error_msg}", show_alert=True)
            return

        # 创建女孩记录（现在包含多语言翻译）
        result = create_girl_record(girl_data)

        if result[0]:
            girl = result[1]

            # 先通过medias.get_media获取媒体信息并发送给客服
            if girl.picture:
                try:
                    media_list = await medias.get_media(callback_query.message._client,
                                                        girl.picture.strip(','),
                                                        "📸 女孩照片")
                    await clients['edenmanbot'].send_media_group(callback_query.message.chat.id, media_list)
                except Exception as e:
                    sys_log.error_log(f"获取媒体信息失败: {e}")

            # 然后发送完整的女孩资料
            card_text = format_girl_staff(girl)
            keyboard = get_keyboard_common("girl_status_confirmation", girl_id=girl.id)
            await clients['edenmanbot'].send_message(callback_query.message.chat.id, card_text, reply_markup=keyboard)
        else:
            await callback_query.answer(f"{e_warning} 创建失败: {result[1]}", show_alert=True)

    except Exception as e:
        sys_log.error_log(f"处理完成录入失败: {e}")
        await callback_query.answer(f"{e_warning} 处理失败")


def create_girl_record(girl_data: Dict[str, str]) -> tuple:
    """创建女孩记录"""
    try:
        # 数据转换 - 按数据库字段顺序排列
        processed_data = {
            'name': girl_data.get('name', ''),
            'country': mean('country_options', girl_data.get('country'), 'from'),  # 使用 mean 方法转换国籍
            'age': int(girl_data.get('age', 0)),
            'height': int(girl_data.get('height', 0)),
            'weight': int(girl_data.get('weight', 0)),
            'boobs': girl_data.get('boobs', ''),
            'baby': mean('baby_options', girl_data.get('baby'), 'from'),  # 使用 mean 方法转换孩子状态
            'location': girl_data.get('location', ''),
            'special': girl_data.get('special', ''),
            'phone': girl_data.get('phone', ''),
            'price_in': girl_data.get('price_in', ''),
            'price_out': girl_data.get('price_out', ''),
            'picture': girl_data.get('picture', ''),  # 现在是必填字段
            'grade': 0,
            'status': 0,
            'note': girl_data.get('note', '')
        }
        new_girl = girls.new(processed_data)
        return [True, new_girl] if new_girl else [False, '创建女孩记录失败']

    except Exception as e:
        sys_log.error_log(f"创建女孩失败: {e}")
        return [False, '创建过程出错']


# format_girl_card 函数已移至 common.py 中的 format_girl_staff 函数
# 使用方式：format_girl_staff(girl)


async def handle_status_confirmation(callback_query: CallbackQuery, status: int):
    """处理状态确认 - 优化版本"""
    try:
        message_text = callback_query.message.text

        # 优化1：使用正则表达式一次性获取girl_id，效率更高
        match = re.search(r'编号: (\d+)', message_text)
        girl_id = int(match.group(1))
        girl = girls[girl_id]

        if girl:
            girl.status = status

            # 优化2：通过mean方法获取状态名称
            status_name = mean('girl_status', status, 'to')

            # 新增：如果状态是在线，发送到多语言频道
            if status == 1:
                try:
                    # channel_manager = get_girl_channel_manager()
                    results = await channel_manager.send_girl_to_channels(girl)
                    if results:
                        sys_log.write_log(f"女孩卡片已发送到多语言频道: 女孩ID {girl.id}, 结果: {results}")
                except Exception as e:
                    sys_log.error_log(f"发送女孩卡片到频道失败: 女孩ID {girl.id}, 错误: {e}")

            # 优化3：使用正则表达式直接替换提示词，简洁高效
            pattern = r'💡 .*'
            updated_text = re.sub(pattern, f"💡 女孩状态已经调整为[{status_name}]", message_text, 1)

            # 使用统一的键盘管理
            keyboard = get_keyboard_common('girl_management', girl_id=girl.id)

            await callback_query.edit_message_text(updated_text, reply_markup=keyboard)

        else:
            await callback_query.answer(f"{e_warning} 找不到女孩记录", show_alert=True)

    except Exception as e:
        sys_log.error_log(f"处理状态确认失败: {e}")
        await callback_query.answer(f"{e_warning} 确认失败")
