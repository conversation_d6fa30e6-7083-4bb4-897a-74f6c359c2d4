# -*- coding: utf-8 -*-
"""
Hazoo模块统一导入文件 - 优化版
提供便捷的统一导入接口，导入所有可安全导入的模块
解决循环导入问题，简化用户使用
"""
__author__ = 'Kobee.li'

# 从类型模块导入类型定义
from ..types import TGirls

# 导入数据模型类
from .girls import Girls
from .guests import Guests

# 导入服务实例
from .feature_translator import featureTranslator
from .girl_channel_manager import girl_channel_manager

# 统一导出，方便外部导入
__all__ = [
    # 数据模型
    'Girls',
    'TGirls',
    'Guests',
    # 服务实例
    'featureTranslator',
    'girl_channel_manager',
]

# 使用方式：
# from plugins.hazoo.modules import Girls, TGirls, girl_channel_manager, featureTranslator
#
# 注意：girls_manage 处理器函数仍建议直接导入以保持清晰性：
# from plugins.hazoo.modules.girls_manage import handle_girl_add_start
