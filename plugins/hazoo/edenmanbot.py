# -*- coding: utf-8 -*-
"""
edenmanbot 主文件 - 包含公共功能
"""
__author__ = 'Kobee.li'

# 1. 首先导入 Pyrogram 核心类（确保不被覆盖）
from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery

# 2. 导入系统基础功能
from shared.classes2 import sys_log

from plugins.hazoo.common import *
# 3. 明确导入 common 中需要的内容
# from plugins.hazoo.common import (
#     clients,        # 客户端字典
#     params,         # 参数管理
#     e_warning,      # 表情符号
#     e_succ,         # 表情符号
#     e_time1,        # 表情符号
#     e_no,           # 表情符号
#     e_ok,           # 表情符号
#     e_flower,       # 表情符号
#     get_keyboard_common,  # 键盘生成函数
# )

# 4. 其他导入
from shared.unix_broker import unix_broker, unix_handler



# 单字符消息处理器 - 触发主菜单（使用 filters.user 权限控制）
@Client.on_message(filters.text & filters.private & filters.incoming & filters.user(params['hazoo.kefu']) & ~filters.reply)
async def handle_single_char_message(client: Client, message: Message):
    """处理单字符消息，显示主菜单"""
    try:
        # 检查是否是单字符消息
        if len(message.text.strip()) != 1:
            return

        await message.reply_text(
            f"{e_flower} 欢迎进入<b>伊甸园</b>管理系统 {e_flower}",
            reply_markup=get_keyboard_common('main_menu')
        )

    except Exception as e:
        sys_log.error_log(f"处理单字符消息失败: {e}")


