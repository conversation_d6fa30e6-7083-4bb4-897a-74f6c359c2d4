# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'

# 1. 首先导入 Pyrogram 核心类（确保不被覆盖）
from pyrogram import Client, filters
from pyrogram.types import Message, CallbackQuery

# 2. 导入系统基础功能
from shared.classes2 import sys_log

from plugins.hazoo.common import *
# # 3. 明确导入 common 中需要的内容
# from plugins.hazoo.common import (
#     clients,        # 客户端字典
#     params,         # 参数管理
#     e_warning,      # 表情符号
#     e_succ,         # 表情符号
#     e_time1,        # 表情符号
# )

# 4. 其他导入
from shared.unix_broker import unix_broker

@Client.on_message(filters.command("start") & filters.private)
async def start_command3(client: Client, message: Message):
    """处理 /start 命令"""
    try:
        sys_log.debug_log(f"🎯 edengirlbot start_command3 被触发! 消息: {message.text}")

        # 添加实际的回复逻辑
        await message.reply_text(
            f"✅ edengirlbot /start 命令工作正常！\n"
            f"用户ID: {message.from_user.id}\n"
            f"用户名: {message.from_user.username or '无用户名'}",
            quote=False
        )

        sys_log.debug_log(f"start_command3 处理完成")

    except Exception as e:
        sys_log.error_log(f"start_command3 处理异常: {e}")
        try:
            await message.reply_text(f" 处理 /start 命令时发生错误", quote=False)
        except:
            pass

